# OuiMed - Senegalese Clinic Health Management System

A minimal, pragmatic Flask application for managing patient records and consultations in a Senegalese clinic.

## Features

- Patient management
- Consultation tracking
- Simple, clean interface
- Production-ready architecture

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -e .[dev]
   ```

2. **Set up environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Initialize database:**
   ```bash
   python -c "from app import create_app; from workers.database import init_db; app = create_app(); init_db(app)"
   ```

4. **Run the application:**
   ```bash
   python -m flask --app app run --debug
   ```

5. **Run tests:**
   ```bash
   pytest
   ```

## Architecture

This application follows clean architecture principles:

- **Domain**: Business models and entities
- **Use Cases**: Application-specific logic  
- **Repositories**: Data access layer
- **Entry Points**: Flask routes and controllers
- **Workers**: Shared utilities and helpers

## Development

- **Code Style**: Black formatter, Flake8 linter
- **Type Checking**: MyPy
- **Testing**: Pytest with 100% coverage requirement
- **Database**: SQLite (development), PostgreSQL (production)

## License

MIT License - see LICENSE file for details.

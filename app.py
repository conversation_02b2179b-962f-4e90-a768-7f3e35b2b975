from flask import Flask, render_template, jsonify
from flask_sqlalchemy import SQLAlchemy
from config import get_config
from workers.logging_config import setup_logging
from typing import Optional

db = SQLAlchemy()


def create_app(config_name: Optional[str] = None) -> Flask:
    app = Flask(__name__)
    config_class = get_config(config_name)
    app.config.from_object(config_class)
    db.init_app(app)
    setup_logging(app)
    register_error_handlers(app)
    register_routes(app)
    with app.app_context():
        db.create_all()

    return app


def register_error_handlers(app: Flask) -> None:
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template("errors/404.html"), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template("errors/500.html"), 500


def register_routes(app: Flask) -> None:
    @app.route("/")
    def index():
        return render_template("index.html")

    @app.route("/health")
    def health_check():
        return jsonify(
            {
                "status": "healthy",
                "database": "connected" if db.engine else "disconnected",
            }
        )

    @app.route("/static/<path:filename>")
    def static_files(filename):
        return app.send_static_file(filename)


if __name__ == "__main__":
    app = create_app()
    app.run(debug=True)

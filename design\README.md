# 🩺 Health Management System - Static Design

## 📁 Structure des fichiers

```
design/
├── index.html              # Tableau de bord principal
├── patients.html           # Liste des patients
├── add-patient.html        # Formulaire nouveau patient
├── patient-profile.html    # Profil détaillé du patient
├── add-record.html         # Formulaire nouveau dossier médical
├── edit-record.html        # Modification dossier médical
├── search-records.html     # Recherche de dossiers
├── assets/
│   └── styles.css         # Styles CSS personnalisés
└── README.md              # Cette documentation
```

## 🎨 Système de design

### Couleurs principales
- **Bleu principal**: `bg-blue-600` (#2563eb) - Boutons primaires, navigation active
- **Vert**: `bg-green-600` (#16a34a) - Actions positives, consultations
- **Rouge**: `bg-red-600` (#dc2626) - Actions dangereuses, urgences
- **Gris**: `bg-gray-50` (#f9fafb) - Arrière-plan, éléments neutres

### Typographie
- **Titres principaux**: `text-2xl font-bold text-gray-900`
- **Sous-titres**: `text-lg font-medium text-gray-900`
- **Texte normal**: `text-sm text-gray-900`
- **Texte secondaire**: `text-sm text-gray-500`

### Composants réutilisables

#### Boutons
```html
<!-- Bouton principal -->
<button class="btn-primary">Action principale</button>

<!-- Bouton secondaire -->
<button class="btn-secondary">Action secondaire</button>

<!-- Bouton danger -->
<button class="btn-danger">Action dangereuse</button>
```

#### Badges de statut
```html
<!-- Consultation normale -->
<span class="badge-consultation">Consultation</span>

<!-- Urgence -->
<span class="badge-urgence">Urgence</span>

<!-- Suivi -->
<span class="badge-suivi">Suivi</span>
```

#### Avatars patients
```html
<!-- Avatar bleu -->
<div class="avatar-blue">
    <span class="text-sm font-medium text-white">AS</span>
</div>
```

## 📱 Responsive Design

### Points de rupture
- **Mobile**: < 768px
- **Tablette**: 768px - 1024px
- **Desktop**: > 1024px

### Navigation mobile
- Navigation fixe en bas d'écran sur mobile
- Menu hamburger pour les écrans < 768px
- Adaptation automatique des grilles et tableaux

## 🔧 Fonctionnalités implémentées

### Pages principales
1. **Tableau de bord** (`index.html`)
   - Statistiques générales
   - Actions rapides
   - Activité récente

2. **Liste des patients** (`patients.html`)
   - Tableau responsive avec pagination
   - Recherche et filtres
   - Actions rapides par patient

3. **Nouveau patient** (`add-patient.html`)
   - Formulaire complet avec validation visuelle
   - Sections organisées (personnel, urgence, médical)
   - Navigation claire avec breadcrumbs

4. **Profil patient** (`patient-profile.html`)
   - Informations personnelles
   - Historique médical complet
   - Actions contextuelles

5. **Nouveau dossier** (`add-record.html`)
   - Formulaire médical détaillé
   - Gestion financière et assurance
   - Options de suivi

6. **Modification dossier** (`edit-record.html`)
   - Pré-remplissage des données
   - Historique des modifications
   - Actions de suppression

7. **Recherche** (`search-records.html`)
   - Recherche simple et avancée
   - Filtres multiples
   - Résultats paginés avec export

## 🌍 Localisation française

### Terminologie médicale
- **Consultation**: Visite médicale standard
- **Urgence**: Consultation d'urgence
- **Suivi**: Rendez-vous de suivi
- **Contrôle**: Visite de contrôle

### Contexte sénégalais
- **Monnaie**: CFA Francs
- **Assurances**: IPM, CNSS, Mutuelle de Santé
- **Noms**: Prénoms et noms sénégalais authentiques
- **Téléphones**: Format +221 (Sénégal)

## 🎯 Bonnes pratiques implémentées

### Accessibilité
- Labels appropriés pour tous les champs
- Contraste suffisant (WCAG AA)
- Navigation au clavier
- Structure sémantique HTML5

### UX/UI
- Feedback visuel pour les actions
- États de chargement et d'erreur
- Navigation cohérente
- Hiérarchie visuelle claire

### Performance
- CSS Tailwind via CDN (optimisé)
- Pas de JavaScript (statique)
- Images optimisées (avatars CSS)
- Structure HTML légère

## 🚀 Prochaines étapes

### Intégration Flask
1. Convertir les formulaires HTML en templates Jinja2
2. Ajouter la validation côté serveur
3. Implémenter la base de données
4. Ajouter l'authentification

### Améliorations possibles
1. **Thème sombre**: Variante dark mode
2. **Animations**: Transitions CSS subtiles
3. **PWA**: Application web progressive
4. **Offline**: Fonctionnement hors ligne

## 📋 Checklist de validation

- [x] 7 pages HTML complètes
- [x] Design responsive (mobile-first)
- [x] Français complet avec contexte sénégalais
- [x] Composants réutilisables
- [x] Navigation cohérente
- [x] Formulaires accessibles
- [x] Données d'exemple réalistes
- [x] Structure sémantique
- [x] Styles Tailwind optimisés
- [x] Documentation complète

## 🔗 Navigation entre les pages

```
index.html (Tableau de bord)
├── patients.html (Liste patients)
│   ├── add-patient.html (Nouveau patient)
│   └── patient-profile.html (Profil patient)
│       ├── add-record.html (Nouveau dossier)
│       └── edit-record.html (Modifier dossier)
└── search-records.html (Recherche)
```

---

**Développé pour**: Clinique Santé Plus, Sénégal  
**Technologies**: HTML5, Tailwind CSS, Design responsive  
**Langue**: Français (contexte sénégalais)

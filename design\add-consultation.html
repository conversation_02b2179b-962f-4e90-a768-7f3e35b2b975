<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouvelle Consultation - Système de Gestion Médicale</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation Header -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-blue-600">Clinique Santé Plus</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="index.html" class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium">
                            Tableau de bord
                        </a>
                        <a href="patients.html" class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium">
                            Patients
                        </a>
                        <a href="add-patient.html" class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium">
                            Nouveau patient
                        </a>
                    </div>
                </div>
                <div class="flex items-center">
                    <span class="text-sm text-gray-500">Dr. Aminata Diallo</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Header Section -->
        <div class="px-4 py-6 sm:px-0">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="patient-profile.html" class="text-gray-400 hover:text-gray-600 mr-4">
                        <span class="text-xl">←</span>
                    </a>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Nouvelle Consultation</h2>
                        <p class="mt-1 text-sm text-gray-600">Patient: Aminata Sow</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                            <span class="text-sm font-medium text-white">AS</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Section -->
        <div class="px-4 sm:px-0">
            <div class="bg-white shadow rounded-lg">
                <form class="p-6">
                    <!-- Progress Steps -->
                    <div class="mb-8">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full">
                                    <span class="text-white text-sm font-medium">1</span>
                                </div>
                                <span class="ml-2 text-sm font-medium text-blue-600">Consultation</span>
                            </div>
                            <div class="flex-1 mx-4 h-0.5 bg-gray-200"></div>
                            <div class="flex items-center">
                                <div class="flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full">
                                    <span class="text-gray-500 text-sm font-medium">2</span>
                                </div>
                                <span class="ml-2 text-sm text-gray-500">Financier</span>
                            </div>
                            <div class="flex-1 mx-4 h-0.5 bg-gray-200"></div>
                            <div class="flex items-center">
                                <div class="flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full">
                                    <span class="text-gray-500 text-sm font-medium">3</span>
                                </div>
                                <span class="ml-2 text-sm text-gray-500">Suivi</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Medical Information -->
                    <div id="step-1" class="space-y-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Left Column -->
                            <div class="space-y-6">
                                <div>
                                    <label for="date_consultation" class="block text-sm font-medium text-gray-700 mb-1">
                                        Date et heure <span class="text-red-500">*</span>
                                    </label>
                                    <input type="datetime-local" id="date_consultation" name="date_consultation" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>

                                <div>
                                    <label for="type_consultation" class="block text-sm font-medium text-gray-700 mb-1">
                                        Type de consultation <span class="text-red-500">*</span>
                                    </label>
                                    <select id="type_consultation" name="type_consultation" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Sélectionner</option>
                                        <option value="consultation">Consultation générale</option>
                                        <option value="urgence">Urgence</option>
                                        <option value="suivi">Suivi</option>
                                        <option value="controle">Contrôle</option>
                                        <option value="vaccination">Vaccination</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="symptomes" class="block text-sm font-medium text-gray-700 mb-1">
                                        Symptômes présentés
                                    </label>
                                    <textarea id="symptomes" name="symptomes" rows="3"
                                              placeholder="Décrivez les symptômes du patient..."
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                                </div>

                                <div>
                                    <label for="diagnostic" class="block text-sm font-medium text-gray-700 mb-1">
                                        Diagnostic
                                    </label>
                                    <textarea id="diagnostic" name="diagnostic" rows="2"
                                              placeholder="Diagnostic médical..."
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="space-y-6">
                                <div>
                                    <label for="notes_medecin" class="block text-sm font-medium text-gray-700 mb-1">
                                        Notes du médecin <span class="text-red-500">*</span>
                                    </label>
                                    <textarea id="notes_medecin" name="notes_medecin" rows="6" required
                                              placeholder="Notes détaillées sur la consultation, observations, recommandations..."
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                                </div>

                                <div>
                                    <label for="medicaments" class="block text-sm font-medium text-gray-700 mb-1">
                                        Médicaments prescrits
                                    </label>
                                    <textarea id="medicaments" name="medicaments" rows="4"
                                              placeholder="Liste des médicaments avec posologie (ex: Paracétamol 500mg - 3 fois par jour)"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                                </div>

                                <div>
                                    <label for="examens_demandes" class="block text-sm font-medium text-gray-700 mb-1">
                                        Examens demandés
                                    </label>
                                    <textarea id="examens_demandes" name="examens_demandes" rows="2"
                                              placeholder="Analyses, radiographies, etc."
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Step Navigation -->
                        <div class="flex justify-end pt-6 border-t border-gray-200">
                            <button type="button" onclick="nextStep(2)" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                                Suivant: Informations financières →
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Financial Information (Hidden by default) -->
                    <div id="step-2" class="space-y-6 hidden">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <label for="assurance" class="block text-sm font-medium text-gray-700 mb-1">
                                    Compagnie d'assurance
                                </label>
                                <select id="assurance" name="assurance"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Aucune assurance</option>
                                    <option value="ipm">IPM (Institut de Prévoyance Maladie)</option>
                                    <option value="cnss">CNSS</option>
                                    <option value="mutuelle_sante">Mutuelle de Santé</option>
                                    <option value="assurance_privee">Assurance Privée</option>
                                    <option value="autre">Autre</option>
                                </select>
                            </div>

                            <div>
                                <label for="numero_assurance" class="block text-sm font-medium text-gray-700 mb-1">
                                    Numéro d'assurance
                                </label>
                                <input type="text" id="numero_assurance" name="numero_assurance"
                                       placeholder="Numéro de carte d'assurance"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label for="frais_total" class="block text-sm font-medium text-gray-700 mb-1">
                                    Frais total de consultation <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <input type="number" id="frais_total" name="frais_total" required min="0"
                                           placeholder="0"
                                           class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">CFA</span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="montant_assurance" class="block text-sm font-medium text-gray-700 mb-1">
                                    Montant couvert par l'assurance
                                </label>
                                <div class="relative">
                                    <input type="number" id="montant_assurance" name="montant_assurance" min="0"
                                           placeholder="0"
                                           class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">CFA</span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="frais_patient" class="block text-sm font-medium text-gray-700 mb-1">
                                    Frais payés par le patient
                                </label>
                                <div class="relative">
                                    <input type="number" id="frais_patient" name="frais_patient" min="0"
                                           placeholder="0"
                                           class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">CFA</span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="mode_paiement" class="block text-sm font-medium text-gray-700 mb-1">
                                    Mode de paiement
                                </label>
                                <select id="mode_paiement" name="mode_paiement"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="especes">Espèces</option>
                                    <option value="carte">Carte bancaire</option>
                                    <option value="mobile_money">Mobile Money</option>
                                    <option value="cheque">Chèque</option>
                                    <option value="virement">Virement</option>
                                </select>
                            </div>
                        </div>

                        <!-- Step Navigation -->
                        <div class="flex justify-between pt-6 border-t border-gray-200">
                            <button type="button" onclick="prevStep(1)" 
                                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                ← Précédent
                            </button>
                            <button type="button" onclick="nextStep(3)" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                                Suivant: Suivi →
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Follow-up (Hidden by default) -->
                    <div id="step-3" class="space-y-6 hidden">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <label for="prochain_rdv" class="block text-sm font-medium text-gray-700 mb-1">
                                    Prochain rendez-vous
                                </label>
                                <input type="datetime-local" id="prochain_rdv" name="prochain_rdv"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label for="rappel" class="block text-sm font-medium text-gray-700 mb-1">
                                    Rappel patient
                                </label>
                                <select id="rappel" name="rappel"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Aucun rappel</option>
                                    <option value="1_jour">1 jour avant</option>
                                    <option value="3_jours">3 jours avant</option>
                                    <option value="1_semaine">1 semaine avant</option>
                                </select>
                            </div>
                        </div>

                        <!-- Final Actions -->
                        <div class="flex justify-between pt-6 border-t border-gray-200">
                            <button type="button" onclick="prevStep(2)" 
                                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                ← Précédent
                            </button>
                            <div class="flex space-x-3">
                                <a href="patient-profile.html" 
                                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    Annuler
                                </a>
                                <button type="button"
                                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    Enregistrer comme brouillon
                                </button>
                                <button type="submit"
                                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                                    Enregistrer la consultation
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <script>
        function nextStep(step) {
            // Hide all steps
            document.getElementById('step-1').classList.add('hidden');
            document.getElementById('step-2').classList.add('hidden');
            document.getElementById('step-3').classList.add('hidden');
            
            // Show target step
            document.getElementById('step-' + step).classList.remove('hidden');
            
            // Update progress indicators
            updateProgress(step);
        }
        
        function prevStep(step) {
            nextStep(step);
        }
        
        function updateProgress(activeStep) {
            // Reset all steps
            for (let i = 1; i <= 3; i++) {
                const circle = document.querySelector(`div:nth-child(${i * 2 - 1}) .w-8`);
                const text = document.querySelector(`div:nth-child(${i * 2 - 1}) span:last-child`);
                
                if (i <= activeStep) {
                    circle.className = 'flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full';
                    circle.querySelector('span').className = 'text-white text-sm font-medium';
                    text.className = 'ml-2 text-sm font-medium text-blue-600';
                } else {
                    circle.className = 'flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full';
                    circle.querySelector('span').className = 'text-gray-500 text-sm font-medium';
                    text.className = 'ml-2 text-sm text-gray-500';
                }
            }
        }
    </script>
</body>
</html>

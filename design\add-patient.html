<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Nouveau Patient - Système de Gestion Médicale</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- Navigation Header -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-blue-600">
                Clinique Santé Plus
              </h1>
            </div>
            <div class="hidden md:ml-6 md:flex md:space-x-8">
              <a
                href="index.html"
                class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium"
              >
                Tableau de bord
              </a>
              <a
                href="patients.html"
                class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium"
              >
                Patients
              </a>
              <a
                href="add-patient.html"
                class="text-blue-600 border-b-2 border-blue-600 px-1 pt-1 pb-4 text-sm font-medium"
              >
                Nouveau patient
              </a>
            </div>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-500">Dr. Aminata Diallo</span>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Header Section -->
      <div class="px-4 py-6 sm:px-0">
        <div class="flex items-center">
          <a
            href="patients.html"
            class="text-gray-400 hover:text-gray-600 mr-4"
          >
            <span class="text-xl">←</span>
          </a>
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Nouveau Patient</h2>
            <p class="mt-1 text-sm text-gray-600">
              Enregistrer les informations d'un nouveau patient
            </p>
          </div>
        </div>
      </div>

      <!-- Form Section -->
      <div class="px-4 sm:px-0">
        <div class="bg-white shadow rounded-lg">
          <form class="space-y-6 p-6">
            <!-- Personal Information Section -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                Informations personnelles
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    for="nom_complet"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Nom complet <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="nom_complet"
                    name="nom_complet"
                    required
                    placeholder="Ex: Aminata Sow"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label
                    for="date_naissance"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Date de naissance <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="date"
                    id="date_naissance"
                    name="date_naissance"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label
                    for="sexe"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Sexe <span class="text-red-500">*</span>
                  </label>
                  <select
                    id="sexe"
                    name="sexe"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Sélectionner</option>
                    <option value="homme">Homme</option>
                    <option value="femme">Femme</option>
                    <option value="autre">Autre</option>
                  </select>
                </div>

                <div>
                  <label
                    for="telephone"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Téléphone <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="tel"
                    id="telephone"
                    name="telephone"
                    required
                    placeholder="Ex: +221 77 123 4567"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Email (optionnel)
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    placeholder="Ex: <EMAIL>"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div class="md:col-span-2">
                  <label
                    for="adresse"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Adresse <span class="text-red-500">*</span>
                  </label>
                  <textarea
                    id="adresse"
                    name="adresse"
                    rows="3"
                    required
                    placeholder="Ex: Quartier Liberté 6, Rue 15, Dakar"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  ></textarea>
                </div>
              </div>
            </div>

            <!-- Emergency Contact Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                Contact d'urgence (optionnel)
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    for="contact_urgence_nom"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Nom du contact
                  </label>
                  <input
                    type="text"
                    id="contact_urgence_nom"
                    name="contact_urgence_nom"
                    placeholder="Ex: Fatou Sow"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label
                    for="contact_urgence_telephone"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Téléphone du contact
                  </label>
                  <input
                    type="tel"
                    id="contact_urgence_telephone"
                    name="contact_urgence_telephone"
                    placeholder="Ex: +221 70 987 6543"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label
                    for="relation"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Relation
                  </label>
                  <select
                    id="relation"
                    name="relation"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Sélectionner</option>
                    <option value="parent">Parent</option>
                    <option value="conjoint">Conjoint(e)</option>
                    <option value="enfant">Enfant</option>
                    <option value="frere_soeur">Frère/Sœur</option>
                    <option value="ami">Ami(e)</option>
                    <option value="autre">Autre</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Medical Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                Informations médicales (optionnel)
              </h3>
              <div class="space-y-4">
                <div>
                  <label
                    for="allergies"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Allergies connues
                  </label>
                  <textarea
                    id="allergies"
                    name="allergies"
                    rows="2"
                    placeholder="Ex: Pénicilline, arachides..."
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  ></textarea>
                </div>

                <div>
                  <label
                    for="antecedents"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Antécédents médicaux
                  </label>
                  <textarea
                    id="antecedents"
                    name="antecedents"
                    rows="2"
                    placeholder="Ex: Diabète, hypertension..."
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  ></textarea>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="border-t border-gray-200 pt-6">
              <div
                class="flex flex-col sm:flex-row sm:justify-end sm:space-x-3 space-y-3 sm:space-y-0"
              >
                <a
                  href="patients.html"
                  class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Annuler
                </a>
                <button
                  type="submit"
                  class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Enregistrer le patient
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </main>
  </body>
</html>

/* Custom styles for reusable components */

/* Navigation active state enhancement */
.nav-active {
    @apply text-blue-600 border-b-2 border-blue-600;
}

/* Card hover effects */
.card-hover {
    @apply transition-all duration-200 hover:shadow-md;
}

/* Button variants */
.btn-primary {
    @apply inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

.btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

.btn-danger {
    @apply inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500;
}

/* Form input focus states */
.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

/* Status badges */
.badge-consultation {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800;
}

.badge-urgence {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800;
}

.badge-suivi {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
}

.badge-controle {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800;
}

/* Patient avatar colors */
.avatar-blue {
    @apply h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center;
}

.avatar-green {
    @apply h-10 w-10 rounded-full bg-green-500 flex items-center justify-center;
}

.avatar-purple {
    @apply h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center;
}

.avatar-orange {
    @apply h-10 w-10 rounded-full bg-orange-500 flex items-center justify-center;
}

/* Mobile responsive navigation */
@media (max-width: 768px) {
    .mobile-nav {
        @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2;
    }
    
    .mobile-nav-item {
        @apply flex flex-col items-center py-2 text-xs text-gray-600;
    }
    
    .mobile-nav-item.active {
        @apply text-blue-600;
    }
}

/* Print styles for medical records */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-before: always;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .print-header {
        border-bottom: 2px solid #000;
        margin-bottom: 20px;
        padding-bottom: 10px;
    }
}

<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Modifier Consultation - Système de Gestion Médicale</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- Navigation Header -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-blue-600">
                Clinique Santé Plus
              </h1>
            </div>
            <div class="hidden md:ml-6 md:flex md:space-x-8">
              <a
                href="index.html"
                class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium"
              >
                Tableau de bord
              </a>
              <a
                href="patients.html"
                class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium"
              >
                Patients
              </a>
              <a
                href="add-patient.html"
                class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium"
              >
                Nouveau patient
              </a>
            </div>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-500">Dr. Aminata Diallo</span>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Header Section -->
      <div class="px-4 py-6 sm:px-0">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <a
              href="patient-profile.html"
              class="text-gray-400 hover:text-gray-600 mr-4"
            >
              <span class="text-xl">←</span>
            </a>
            <div>
              <h2 class="text-2xl font-bold text-gray-900">
                Modifier Consultation
              </h2>
              <p class="mt-1 text-sm text-gray-600">
                Patient: Aminata Sow - Consultation du 15 Janvier 2024
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
            >
              Consultation
            </span>
          </div>
        </div>
      </div>

      <!-- Form Section -->
      <div class="px-4 sm:px-0">
        <div class="bg-white shadow rounded-lg">
          <form class="space-y-6 p-6">
            <!-- Patient Information (Read-only) -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                Informations du patient
              </h3>
              <div class="bg-gray-50 rounded-lg p-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <span class="text-sm font-medium text-gray-500"
                      >Patient:</span
                    >
                    <p class="text-sm text-gray-900">Aminata Sow</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-500"
                      >Date de consultation:</span
                    >
                    <p class="text-sm text-gray-900">15 Janvier 2024, 14:30</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-500"
                      >ID Dossier:</span
                    >
                    <p class="text-sm text-gray-900">#R001247</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Medical Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                Informations médicales
              </h3>
              <div class="space-y-6">
                <div>
                  <label
                    for="type_consultation"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Type de consultation <span class="text-red-500">*</span>
                  </label>
                  <select
                    id="type_consultation"
                    name="type_consultation"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="consultation" selected>
                      Consultation générale
                    </option>
                    <option value="urgence">Urgence</option>
                    <option value="suivi">Suivi</option>
                    <option value="controle">Contrôle</option>
                    <option value="vaccination">Vaccination</option>
                  </select>
                </div>

                <div>
                  <label
                    for="symptomes"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Symptômes présentés
                  </label>
                  <textarea
                    id="symptomes"
                    name="symptomes"
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
Maux de tête légers, fatigue générale</textarea
                  >
                </div>

                <div>
                  <label
                    for="diagnostic"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Diagnostic
                  </label>
                  <textarea
                    id="diagnostic"
                    name="diagnostic"
                    rows="2"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
Hypertension artérielle légère</textarea
                  >
                </div>

                <div>
                  <label
                    for="notes_medecin"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Notes du médecin <span class="text-red-500">*</span>
                  </label>
                  <textarea
                    id="notes_medecin"
                    name="notes_medecin"
                    rows="4"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
Contrôle de routine. Tension artérielle légèrement élevée (140/90). Recommandation de réduire le sel et faire plus d'exercice. Patient en bonne santé générale.</textarea
                  >
                </div>

                <div>
                  <label
                    for="medicaments"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Médicaments prescrits
                  </label>
                  <textarea
                    id="medicaments"
                    name="medicaments"
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
Amlodipine 5mg - 1 comprimé par jour le matin
Paracétamol 500mg - selon besoin pour les maux de tête</textarea
                  >
                </div>

                <div>
                  <label
                    for="examens_demandes"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Examens demandés
                  </label>
                  <textarea
                    id="examens_demandes"
                    name="examens_demandes"
                    rows="2"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
Bilan lipidique dans 3 mois</textarea
                  >
                </div>
              </div>
            </div>

            <!-- Financial Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                Informations financières
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    for="assurance"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Compagnie d'assurance
                  </label>
                  <select
                    id="assurance"
                    name="assurance"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Aucune assurance</option>
                    <option value="ipm" selected>
                      IPM (Institut de Prévoyance Maladie)
                    </option>
                    <option value="cnss">CNSS</option>
                    <option value="mutuelle_sante">Mutuelle de Santé</option>
                    <option value="assurance_privee">Assurance Privée</option>
                    <option value="autre">Autre</option>
                  </select>
                </div>

                <div>
                  <label
                    for="numero_assurance"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Numéro d'assurance
                  </label>
                  <input
                    type="text"
                    id="numero_assurance"
                    name="numero_assurance"
                    value="IPM123456789"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label
                    for="frais_total"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Frais total de consultation
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      id="frais_total"
                      name="frais_total"
                      required
                      min="0"
                      value="15000"
                      class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <div
                      class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
                    >
                      <span class="text-gray-500 sm:text-sm">CFA</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label
                    for="montant_assurance"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Montant couvert par l'assurance
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      id="montant_assurance"
                      name="montant_assurance"
                      min="0"
                      value="10000"
                      class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <div
                      class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
                    >
                      <span class="text-gray-500 sm:text-sm">CFA</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label
                    for="frais_patient"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Frais payés par le patient
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      id="frais_patient"
                      name="frais_patient"
                      min="0"
                      value="5000"
                      class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <div
                      class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
                    >
                      <span class="text-gray-500 sm:text-sm">CFA</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label
                    for="mode_paiement"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Mode de paiement
                  </label>
                  <select
                    id="mode_paiement"
                    name="mode_paiement"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="especes" selected>Espèces</option>
                    <option value="carte">Carte bancaire</option>
                    <option value="mobile_money">Mobile Money</option>
                    <option value="cheque">Chèque</option>
                    <option value="virement">Virement</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Follow-up Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Suivi</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    for="prochain_rdv"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Prochain rendez-vous
                  </label>
                  <input
                    type="datetime-local"
                    id="prochain_rdv"
                    name="prochain_rdv"
                    value="2024-02-15T14:00"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label
                    for="rappel"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Rappel patient
                  </label>
                  <select
                    id="rappel"
                    name="rappel"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Aucun rappel</option>
                    <option value="1_jour">1 jour avant</option>
                    <option value="3_jours" selected>3 jours avant</option>
                    <option value="1_semaine">1 semaine avant</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Modification History -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                Historique des modifications
              </h3>
              <div class="bg-gray-50 rounded-lg p-4">
                <div class="space-y-2">
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600">Créé le:</span>
                    <span class="text-gray-900"
                      >15 Janvier 2024, 14:45 par Dr. Aminata Diallo</span
                    >
                  </div>
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600">Dernière modification:</span>
                    <span class="text-gray-900"
                      >15 Janvier 2024, 15:20 par Dr. Aminata Diallo</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="border-t border-gray-200 pt-6">
              <div
                class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0"
              >
                <div class="flex space-x-3">
                  <button
                    type="button"
                    class="inline-flex items-center px-3 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Supprimer le dossier
                  </button>
                </div>
                <div
                  class="flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0"
                >
                  <a
                    href="patient-profile.html"
                    class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Annuler
                  </a>
                  <button
                    type="submit"
                    class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Enregistrer les modifications
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </main>
  </body>
</html>

<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Liste des Patients - Système de Gestion Médicale</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- Navigation Header -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-blue-600">
                Clinique Santé Plus
              </h1>
            </div>
            <div class="hidden md:ml-6 md:flex md:space-x-8">
              <a
                href="index.html"
                class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium"
              >
                Tableau de bord
              </a>
              <a
                href="patients.html"
                class="text-blue-600 border-b-2 border-blue-600 px-1 pt-1 pb-4 text-sm font-medium"
              >
                Patients
              </a>
              <a
                href="add-patient.html"
                class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium"
              >
                Nouveau patient
              </a>
            </div>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-500">Dr. Aminata Diallo</span>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Header Section -->
      <div class="px-4 py-6 sm:px-0">
        <div
          class="flex flex-col sm:flex-row sm:items-center sm:justify-between"
        >
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Liste des Patients</h2>
            <p class="mt-1 text-sm text-gray-600">
              Gérez et consultez tous vos patients
            </p>
          </div>
          <div class="mt-4 sm:mt-0">
            <a
              href="add-patient.html"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Nouveau patient
            </a>
          </div>
        </div>
      </div>

      <!-- Search and Filter Section -->
      <div class="px-4 sm:px-0 mb-6">
        <div class="bg-white shadow rounded-lg p-6">
          <div class="mb-4">
            <div class="flex items-center space-x-4">
              <button
                onclick="toggleSearchMode('patients')"
                id="btn-patients"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md"
              >
                Rechercher patients
              </button>
              <button
                onclick="toggleSearchMode('records')"
                id="btn-records"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Rechercher dossiers médicaux
              </button>
            </div>
          </div>

          <!-- Patient Search (Default) -->
          <div
            id="patient-search"
            class="grid grid-cols-1 md:grid-cols-3 gap-4"
          >
            <div>
              <label
                for="search"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Rechercher</label
              >
              <input
                type="text"
                id="search"
                name="search"
                placeholder="Nom, téléphone..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label
                for="gender"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Sexe</label
              >
              <select
                id="gender"
                name="gender"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Tous</option>
                <option value="homme">Homme</option>
                <option value="femme">Femme</option>
                <option value="autre">Autre</option>
              </select>
            </div>
            <div class="flex items-end">
              <button
                type="button"
                class="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Filtrer
              </button>
            </div>
          </div>

          <!-- Medical Records Search (Hidden by default) -->
          <div
            id="records-search"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 hidden"
          >
            <div>
              <label
                for="patient-name"
                class="block text-sm font-medium text-gray-700 mb-1"
              >
                Nom du patient
              </label>
              <input
                type="text"
                id="patient-name"
                name="patient-name"
                placeholder="Nom du patient..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label
                for="date-range"
                class="block text-sm font-medium text-gray-700 mb-1"
              >
                Période de consultation
              </label>
              <select
                id="date-range"
                name="date-range"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Toutes les dates</option>
                <option value="today">Aujourd'hui</option>
                <option value="week">Cette semaine</option>
                <option value="month">Ce mois</option>
                <option value="quarter">Ce trimestre</option>
                <option value="year">Cette année</option>
              </select>
            </div>

            <div>
              <label
                for="consultation-type"
                class="block text-sm font-medium text-gray-700 mb-1"
              >
                Type de consultation
              </label>
              <select
                id="consultation-type"
                name="consultation-type"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Tous les types</option>
                <option value="consultation">Consultation générale</option>
                <option value="urgence">Urgence</option>
                <option value="suivi">Suivi</option>
                <option value="controle">Contrôle</option>
                <option value="vaccination">Vaccination</option>
              </select>
            </div>

            <div class="flex items-end">
              <button
                type="button"
                class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Rechercher consultations
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Patients Table -->
      <div class="px-4 sm:px-0">
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Patient
                  </th>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Contact
                  </th>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Âge
                  </th>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Dernière visite
                  </th>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div
                          class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center"
                        >
                          <span class="text-sm font-medium text-white">AS</span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                          Aminata Sow
                        </div>
                        <div class="text-sm text-gray-500">Femme</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">+221 77 123 4567</div>
                    <div class="text-sm text-gray-500">
                      <EMAIL>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    32 ans
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    15 Jan 2024
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a
                      href="patient-profile.html"
                      class="text-blue-600 hover:text-blue-900 mr-3"
                      >Voir</a
                    >
                    <a
                      href="add-record.html"
                      class="text-green-600 hover:text-green-900"
                      >Consultation</a
                    >
                  </td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div
                          class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center"
                        >
                          <span class="text-sm font-medium text-white">MD</span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                          Mamadou Diop
                        </div>
                        <div class="text-sm text-gray-500">Homme</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">+221 70 987 6543</div>
                    <div class="text-sm text-gray-500">
                      <EMAIL>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    45 ans
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    12 Jan 2024
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a
                      href="patient-profile.html"
                      class="text-blue-600 hover:text-blue-900 mr-3"
                      >Voir</a
                    >
                    <a
                      href="add-record.html"
                      class="text-green-600 hover:text-green-900"
                      >Consultation</a
                    >
                  </td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div
                          class="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center"
                        >
                          <span class="text-sm font-medium text-white">FS</span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                          Fatou Sall
                        </div>
                        <div class="text-sm text-gray-500">Femme</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">+221 76 555 1234</div>
                    <div class="text-sm text-gray-500">
                      <EMAIL>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    28 ans
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    10 Jan 2024
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a
                      href="patient-profile.html"
                      class="text-blue-600 hover:text-blue-900 mr-3"
                      >Voir</a
                    >
                    <a
                      href="add-consultation.html"
                      class="text-green-600 hover:text-green-900"
                      >Consultation</a
                    >
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div class="px-4 sm:px-0 mt-6">
        <div
          class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow"
        >
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Précédent
            </button>
            <button
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Suivant
            </button>
          </div>
          <div
            class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
          >
            <div>
              <p class="text-sm text-gray-700">
                Affichage de <span class="font-medium">1</span> à
                <span class="font-medium">10</span> sur
                <span class="font-medium">247</span> résultats
              </p>
            </div>
            <div>
              <nav
                class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
              >
                <button
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  Précédent
                </button>
                <button
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600"
                >
                  1
                </button>
                <button
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  2
                </button>
                <button
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  3
                </button>
                <button
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  Suivant
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </main>

    <script>
      function toggleSearchMode(mode) {
        const patientSearch = document.getElementById("patient-search");
        const recordsSearch = document.getElementById("records-search");
        const btnPatients = document.getElementById("btn-patients");
        const btnRecords = document.getElementById("btn-records");

        if (mode === "patients") {
          patientSearch.classList.remove("hidden");
          recordsSearch.classList.add("hidden");
          btnPatients.className =
            "px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md";
          btnRecords.className =
            "px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300";
        } else {
          patientSearch.classList.add("hidden");
          recordsSearch.classList.remove("hidden");
          btnPatients.className =
            "px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300";
          btnRecords.className =
            "px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md";
        }
      }
    </script>
  </body>
</html>

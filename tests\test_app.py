import pytest
import json
import os
import sys

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app import create_app, db


class TestAppFactory:
    def test_create_app_default(self):
        app = create_app()
        assert app is not None
        assert app.config["TESTING"] is False

    def test_create_app_testing(self):
        app = create_app("testing")
        assert app is not None
        assert app.config["TESTING"] is True

    def test_create_app_development(self):
        app = create_app("development")
        assert app is not None
        assert app.config["DEBUG"] is True


class TestRoutes:
    def test_index_route(self, client):
        response = client.get("/")
        assert response.status_code == 200
        assert "Bienvenue dans OuiMed".encode("utf-8") in response.data

    def test_health_check_route(self, client):
        response = client.get("/health")
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data["status"] == "healthy"
        assert "database" in data

    def test_static_files_route(self, client):
        response = client.get("/static/css/custom.css")
        assert response.status_code == 200


class TestErrorHandlers:
    def test_404_error_handler(self, client):
        response = client.get("/nonexistent-page")
        assert response.status_code == 404
        assert "Page non trouvée".encode("utf-8") in response.data

    def test_500_error_handler(self, app):
        @app.route("/test-500")
        def test_500():
            raise Exception("Test error")

        app.config["TESTING"] = True
        app.config["PROPAGATE_EXCEPTIONS"] = False

        with app.test_client() as test_client:
            response = test_client.get("/test-500")
            assert response.status_code == 500
            assert "Erreur serveur".encode("utf-8") in response.data


class TestDatabase:
    def test_database_initialization(self, app):
        with app.app_context():
            assert db.engine is not None

    def test_database_tables_created(self, app):
        with app.app_context():
            # Since we don't have models yet, just check that db is accessible
            assert db.session is not None

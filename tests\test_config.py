import pytest
import os
from config import BaseConfig, DevelopmentConfig, TestingConfig, ProductionConfig, get_config


class TestBaseConfig:
    def test_base_config_defaults(self):
        config = BaseConfig()
        assert config.SQLALCHEMY_TRACK_MODIFICATIONS is False
        assert config.SQLALCHEMY_RECORD_QUERIES is False
        assert config.WTF_CSRF_ENABLED is True
        assert config.WTF_CSRF_TIME_LIMIT == 3600
        assert config.LOG_LEVEL == "INFO"
        assert "logs/ouimed.log" in config.LOG_FILE


class TestDevelopmentConfig:
    def test_development_config(self):
        config = DevelopmentConfig()
        assert config.DEBUG is True
        assert config.SQLALCHEMY_ECHO is True
        assert "sqlite:///" in config.SQLALCHEMY_DATABASE_URI


class TestTestingConfig:
    def test_testing_config(self):
        config = TestingConfig()
        assert config.TESTING is True
        assert config.DEBUG is False
        assert config.SQLALCHEMY_DATABASE_URI == "sqlite:///:memory:"
        assert config.WTF_CSRF_ENABLED is False


class TestProductionConfig:
    def test_production_config(self):
        config = ProductionConfig()
        assert config.DEBUG is False
        assert "sqlite:///" in config.SQLALCHEMY_DATABASE_URI


class TestGetConfig:
    def test_get_config_development(self):
        config = get_config("development")
        assert config == DevelopmentConfig
    
    def test_get_config_testing(self):
        config = get_config("testing")
        assert config == TestingConfig
    
    def test_get_config_production(self):
        config = get_config("production")
        assert config == ProductionConfig
    
    def test_get_config_default(self):
        config = get_config("nonexistent")
        assert config == DevelopmentConfig
    
    def test_get_config_none(self):
        config = get_config(None)
        assert config == DevelopmentConfig
    
    def test_get_config_from_env(self, monkeypatch):
        monkeypatch.setenv("FLASK_ENV", "testing")
        config = get_config()
        assert config == TestingConfig

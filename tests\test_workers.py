import pytest
import tempfile
import os
import logging
import sys
from unittest.mock import patch, MagicMock

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from workers.database import init_db, reset_db, get_db_info
from workers.logging_config import setup_logging, log_error, log_info, log_warning
from app import create_app, db


class TestDatabase:
    def test_init_db(self, app):
        with app.app_context():
            # Should not raise an exception
            init_db(app)

    def test_reset_db(self, app):
        with app.app_context():
            # Should not raise an exception
            reset_db(app)

    def test_get_db_info(self, app):
        with app.app_context():
            info = get_db_info(app)
            assert "url" in info
            assert "driver" in info
            assert "connected" in info
            assert info["connected"] == "yes"

    def test_get_db_info_error_handling(self, app):
        with app.app_context():
            # Simply test that the function works normally
            info = get_db_info(app)
            assert "connected" in info


class TestLoggingConfig:
    def test_setup_logging_debug_mode(self):
        app = create_app("development")
        app.config["DEBUG"] = True

        # Should not set up file logging in debug mode
        setup_logging(app)
        # No exception should be raised

    def test_setup_logging_testing_mode(self):
        app = create_app("testing")

        # Should not set up file logging in testing mode
        setup_logging(app)
        # No exception should be raised

    @patch("workers.logging_config.os.path.exists")
    @patch("workers.logging_config.os.mkdir")
    def test_setup_logging_production_mode(self, mock_mkdir, mock_exists):
        app = create_app("production")
        app.config["DEBUG"] = False
        app.config["TESTING"] = False

        mock_exists.return_value = False

        setup_logging(app)

        # Check that mkdir was called with "logs" (may be called multiple times)
        mock_mkdir.assert_any_call("logs")

    @patch("workers.logging_config.logging.error")
    def test_log_error_with_exception(self, mock_log_error):
        exception = ValueError("Test exception")
        log_error("Test message", exception)
        mock_log_error.assert_called_once_with("Test message: Test exception")

    @patch("workers.logging_config.logging.error")
    def test_log_error_without_exception(self, mock_log_error):
        log_error("Test message")
        mock_log_error.assert_called_once_with("Test message")

    @patch("workers.logging_config.logging.info")
    def test_log_info(self, mock_log_info):
        log_info("Test info message")
        mock_log_info.assert_called_once_with("Test info message")

    @patch("workers.logging_config.logging.warning")
    def test_log_warning(self, mock_log_warning):
        log_warning("Test warning message")
        mock_log_warning.assert_called_once_with("Test warning message")

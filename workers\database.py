from flask import Flask
from app import db
from typing import Dict
import logging


def init_db(app: Flask) -> None:
    with app.app_context():
        try:
            db.create_all()
            logging.info("Database tables created successfully")
        except Exception as e:
            logging.error(f"Error creating database tables: {e}")
            raise


def reset_db(app: Flask) -> None:
    with app.app_context():
        try:
            db.drop_all()
            db.create_all()
            logging.info("Database reset successfully")
        except Exception as e:
            logging.error(f"Error resetting database: {e}")
            raise


def get_db_info(app: Flask) -> Dict[str, str]:
    with app.app_context():
        try:
            engine = db.engine
            return {
                "url": str(engine.url).replace(engine.url.password or "", "***"),
                "driver": engine.dialect.name,
                "connected": "yes" if engine else "no",
            }
        except Exception as e:
            logging.error(f"Error getting database info: {e}")
            return {"error": str(e)}

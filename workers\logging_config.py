import logging
import os
from logging.handlers import RotatingFile<PERSON><PERSON><PERSON>
from flask import Flask
from typing import Optional


def setup_logging(app: Flask) -> None:
    if app.debug or app.testing:
        return
    os.makedirs("logs", exist_ok=True)
    file_handler = RotatingFileHandler(
        app.config.get("LOG_FILE", "logs/ouimed.log"),
        maxBytes=10240000,
        backupCount=10,
    )

    file_handler.setFormatter(
        logging.Formatter(
            "%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]"
        )
    )

    log_level = getattr(logging, app.config.get("LOG_LEVEL", "INFO").upper())
    file_handler.setLevel(log_level)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(log_level)

    app.logger.info("OuiMed application startup")


def log_error(message: str, exception: Optional[Exception] = None) -> None:
    if exception:
        logging.error(f"{message}: {str(exception)}")
    else:
        logging.error(message)


def log_info(message: str) -> None:
    logging.info(message)


def log_warning(message: str) -> None:
    logging.warning(message)
